import React, { useState } from 'react';
import { X, CreditCard, Banknote, Smartphone, Calculator } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore, PaymentMethod } from '@/stores/posStore';
import { formatCurrency, calculateChange } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  totalAmount: number;
}

const paymentMethods: PaymentMethod[] = [
  { id: 'cash', name: 'Cash', type: 'cash' },
  { id: 'card', name: 'Credit/Debit Card', type: 'card' },
  { id: 'gopay', name: 'GoPay', type: 'digital' },
  { id: 'ovo', name: 'O<PERSON>', type: 'digital' },
  { id: 'dana', name: 'DAN<PERSON>', type: 'digital' },
];

const quickAmounts = [50000, 100000, 200000, 500000];

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  totalAmount,
}) => {
  const { processPayment, isProcessing } = usePOSStore();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(paymentMethods[0]);
  const [paidAmount, setPaidAmount] = useState<string>(totalAmount.toString());
  const [showCalculator, setShowCalculator] = useState(false);

  const paidAmountNumber = parseFloat(paidAmount) || 0;
  const changeAmount = calculateChange(totalAmount, paidAmountNumber);
  const isInsufficientPayment = paidAmountNumber < totalAmount;

  const handlePayment = async () => {
    if (isInsufficientPayment) {
      toast.error('Insufficient payment amount');
      return;
    }

    try {
      await processPayment(paidAmountNumber, selectedPaymentMethod);
      onSuccess();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Payment failed');
    }
  };

  const handleQuickAmount = (amount: number) => {
    setPaidAmount(amount.toString());
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'cash':
        return <Banknote className="h-5 w-5" />;
      case 'card':
        return <CreditCard className="h-5 w-5" />;
      case 'digital':
        return <Smartphone className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Payment</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Total Amount */}
          <div className="text-center p-4 bg-primary-50 rounded-lg">
            <p className="text-sm text-primary-600 mb-1">Total Amount</p>
            <p className="text-2xl font-bold text-primary-900">
              {formatCurrency(totalAmount)}
            </p>
          </div>

          {/* Payment Method Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Payment Method
            </label>
            <div className="grid grid-cols-1 gap-2">
              {paymentMethods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method)}
                  className={`flex items-center p-3 rounded-lg border-2 transition-colors ${
                    selectedPaymentMethod.id === method.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {getPaymentMethodIcon(method.type)}
                  <span className="ml-3 font-medium">{method.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Payment Amount */}
          {selectedPaymentMethod.type === 'cash' && (
            <div>
              <Input
                type="number"
                label="Amount Paid"
                value={paidAmount}
                onChange={(e) => setPaidAmount(e.target.value)}
                placeholder="0"
                min="0"
                step="1000"
                error={isInsufficientPayment ? 'Insufficient payment amount' : undefined}
              />

              {/* Quick Amount Buttons */}
              <div className="mt-3">
                <p className="text-sm font-medium text-gray-700 mb-2">Quick amounts:</p>
                <div className="grid grid-cols-2 gap-2">
                  {quickAmounts.map((amount) => (
                    <Button
                      key={amount}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAmount(amount)}
                      className="text-xs"
                    >
                      {formatCurrency(amount)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Change Amount */}
              {paidAmountNumber > 0 && (
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-green-700">Change:</span>
                    <span className="text-lg font-bold text-green-900">
                      {formatCurrency(changeAmount)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Digital Payment Info */}
          {selectedPaymentMethod.type === 'digital' && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                Customer will pay exactly {formatCurrency(totalAmount)} via {selectedPaymentMethod.name}
              </p>
            </div>
          )}

          {/* Card Payment Info */}
          {selectedPaymentMethod.type === 'card' && (
            <div className="p-4 bg-purple-50 rounded-lg">
              <p className="text-sm text-purple-700">
                Customer will pay exactly {formatCurrency(totalAmount)} via card
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handlePayment}
              className="flex-1"
              loading={isProcessing}
              disabled={isInsufficientPayment || isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Complete Payment'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentModal;

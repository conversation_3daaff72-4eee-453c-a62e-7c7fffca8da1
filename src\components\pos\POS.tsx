import React, { useState } from 'react';
import { Search, ShoppingCart, User, FileText } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore } from '@/stores/posStore';
import { Product } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import Input from '@/components/common/Input';
import Button from '@/components/common/Button';
import ProductGrid from './ProductGrid';
import Cart from './Cart';
import PaymentModal from './PaymentModal';
import ReceiptModal from './ReceiptModal';

// Mock products data - replace with actual API call
const mockProducts: Product[] = [
  {
    id: 1,
    sku: 'CC-330',
    barcode: '1234567890123',
    name: 'Coca Cola 330ml',
    description: 'Refreshing cola drink',
    categoryId: 1,
    costPrice: 3000,
    sellingPrice: 5000,
    currentStock: 50,
    minStock: 10,
    unit: 'pcs',
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 2,
    sku: 'IM-GOR',
    barcode: '1234567890124',
    name: 'Indomie Goreng',
    description: 'Instant fried noodles',
    categoryId: 2,
    costPrice: 2500,
    sellingPrice: 4000,
    currentStock: 100,
    minStock: 20,
    unit: 'pcs',
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 3,
    sku: 'AQ-600',
    barcode: '1234567890125',
    name: 'Aqua 600ml',
    description: 'Mineral water',
    categoryId: 1,
    costPrice: 2000,
    sellingPrice: 3500,
    currentStock: 75,
    minStock: 15,
    unit: 'pcs',
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 4,
    sku: 'BR-WH',
    barcode: '1234567890126',
    name: 'Bread White',
    description: 'Fresh white bread',
    categoryId: 3,
    costPrice: 8000,
    sellingPrice: 12000,
    currentStock: 25,
    minStock: 5,
    unit: 'pcs',
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 5,
    sku: 'MK-VAN',
    barcode: '1234567890127',
    name: 'Milk Vanilla 250ml',
    description: 'Vanilla flavored milk',
    categoryId: 1,
    costPrice: 4000,
    sellingPrice: 6500,
    currentStock: 40,
    minStock: 10,
    unit: 'pcs',
    isActive: true,
    isTrackable: true,
    allowNegativeStock: false,
    isTaxable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const POS: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const { cart, currentTransaction, customerName, setCustomerName, getCartSummary } = usePOSStore();

  const filteredProducts = mockProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode?.includes(searchTerm)
  );

  const cartSummary = getCartSummary();

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setShowReceiptModal(true);
    toast.success('Transaction completed successfully!');
  };

  return (
    <div className="h-full flex flex-col xl:flex-row gap-4 md:gap-6">
      {/* Left Panel - Products */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="mb-4 md:mb-6">
          <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">Point of Sale</h1>

          {/* Search and Customer Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-5 w-5" />}
            />
            <Input
              type="text"
              placeholder="Customer name (optional)"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              leftIcon={<User className="h-5 w-5" />}
            />
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1 overflow-y-auto">
          <ProductGrid products={filteredProducts} />
        </div>
      </div>

      {/* Right Panel - Cart */}
      <div className="w-full xl:w-96 flex flex-col">
        <Card className="flex-1 flex flex-col max-h-[600px] xl:max-h-none">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <ShoppingCart className="h-5 w-5 mr-2" />
              Cart ({cartSummary.itemCount} items)
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col p-0 min-h-0">
            <div className="flex-1 overflow-y-auto px-4 md:px-6">
              <Cart />
            </div>

            {/* Cart Summary */}
            <div className="border-t p-4 md:p-6 space-y-3 bg-gray-50">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(cartSummary.subtotal)}</span>
              </div>
              {cartSummary.discountAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount:</span>
                  <span>-{formatCurrency(cartSummary.discountAmount)}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Tax:</span>
                <span>{formatCurrency(cartSummary.taxAmount)}</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t pt-3">
                <span>Total:</span>
                <span>{formatCurrency(cartSummary.totalAmount)}</span>
              </div>

              <Button
                onClick={handleCheckout}
                className="w-full"
                disabled={cart.length === 0}
                size="lg"
              >
                Checkout
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      {showPaymentModal && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
          totalAmount={cartSummary.totalAmount}
        />
      )}

      {showReceiptModal && currentTransaction && (
        <ReceiptModal
          isOpen={showReceiptModal}
          onClose={() => setShowReceiptModal(false)}
          transaction={currentTransaction}
        />
      )}
    </div>
  );
};

export default POS;

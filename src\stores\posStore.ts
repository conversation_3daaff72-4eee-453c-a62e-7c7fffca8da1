import { create } from 'zustand';
import { Product } from '@/types';
import { generateTransactionNumber, calculateTax, calculateDiscount } from '@/lib/utils';

export interface CartItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount?: number;
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'cash' | 'card' | 'digital';
}

export interface Transaction {
  id: string;
  transactionNumber: string;
  items: CartItem[];
  subtotal: number;
  discountAmount: number;
  discountPercentage: number;
  taxAmount: number;
  taxPercentage: number;
  totalAmount: number;
  paidAmount: number;
  changeAmount: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  notes?: string;
  createdAt: Date;
}

interface POSState {
  cart: CartItem[];
  currentTransaction: Transaction | null;
  discountPercentage: number;
  taxPercentage: number;
  customerName: string;
  notes: string;
  isProcessing: boolean;
}

interface POSActions {
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  setDiscount: (percentage: number) => void;
  setTax: (percentage: number) => void;
  setCustomerName: (name: string) => void;
  setNotes: (notes: string) => void;
  processPayment: (paidAmount: number, paymentMethod: PaymentMethod) => Promise<Transaction>;
  getCartSummary: () => {
    subtotal: number;
    discountAmount: number;
    taxAmount: number;
    totalAmount: number;
    itemCount: number;
  };
}

type POSStore = POSState & POSActions;

export const usePOSStore = create<POSStore>((set, get) => ({
  // State
  cart: [],
  currentTransaction: null,
  discountPercentage: 0,
  taxPercentage: 10, // Default 10% tax
  customerName: '',
  notes: '',
  isProcessing: false,

  // Actions
  addToCart: (product: Product, quantity = 1) => {
    const { cart } = get();
    const existingItem = cart.find(item => item.product.id === product.id);

    if (existingItem) {
      // Update existing item
      const updatedCart = cart.map(item =>
        item.product.id === product.id
          ? {
              ...item,
              quantity: item.quantity + quantity,
              totalPrice: (item.quantity + quantity) * item.unitPrice,
            }
          : item
      );
      set({ cart: updatedCart });
    } else {
      // Add new item
      const newItem: CartItem = {
        product,
        quantity,
        unitPrice: product.sellingPrice,
        totalPrice: quantity * product.sellingPrice,
        discount: 0,
      };
      set({ cart: [...cart, newItem] });
    }
  },

  removeFromCart: (productId: number) => {
    const { cart } = get();
    const updatedCart = cart.filter(item => item.product.id !== productId);
    set({ cart: updatedCart });
  },

  updateQuantity: (productId: number, quantity: number) => {
    if (quantity <= 0) {
      get().removeFromCart(productId);
      return;
    }

    const { cart } = get();
    const updatedCart = cart.map(item =>
      item.product.id === productId
        ? {
            ...item,
            quantity,
            totalPrice: quantity * item.unitPrice,
          }
        : item
    );
    set({ cart: updatedCart });
  },

  clearCart: () => {
    set({
      cart: [],
      discountPercentage: 0,
      customerName: '',
      notes: '',
      currentTransaction: null,
    });
  },

  setDiscount: (percentage: number) => {
    set({ discountPercentage: Math.max(0, Math.min(100, percentage)) });
  },

  setTax: (percentage: number) => {
    set({ taxPercentage: Math.max(0, percentage) });
  },

  setCustomerName: (name: string) => {
    set({ customerName: name });
  },

  setNotes: (notes: string) => {
    set({ notes });
  },

  processPayment: async (paidAmount: number, paymentMethod: PaymentMethod) => {
    const { cart, discountPercentage, taxPercentage, customerName, notes } = get();
    
    if (cart.length === 0) {
      throw new Error('Cart is empty');
    }

    set({ isProcessing: true });

    try {
      const summary = get().getCartSummary();
      
      if (paidAmount < summary.totalAmount) {
        throw new Error('Insufficient payment amount');
      }

      const transaction: Transaction = {
        id: Date.now().toString(),
        transactionNumber: generateTransactionNumber(),
        items: [...cart],
        subtotal: summary.subtotal,
        discountAmount: summary.discountAmount,
        discountPercentage,
        taxAmount: summary.taxAmount,
        taxPercentage,
        totalAmount: summary.totalAmount,
        paidAmount,
        changeAmount: paidAmount - summary.totalAmount,
        paymentMethod,
        customerName: customerName || undefined,
        notes: notes || undefined,
        createdAt: new Date(),
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      set({
        currentTransaction: transaction,
        isProcessing: false,
      });

      // Clear cart after successful transaction
      get().clearCart();

      return transaction;
    } catch (error) {
      set({ isProcessing: false });
      throw error;
    }
  },

  getCartSummary: () => {
    const { cart, discountPercentage, taxPercentage } = get();
    
    const subtotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);
    const discountAmount = calculateDiscount(subtotal, discountPercentage);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = calculateTax(taxableAmount, taxPercentage);
    const totalAmount = taxableAmount + taxAmount;
    const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

    return {
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount,
      itemCount,
    };
  },
}));

import React from 'react';
import { Minus, Plus, Trash2, ShoppingCart, Percent } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore } from '@/stores/posStore';
import { formatCurrency } from '@/lib/utils';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';

const Cart: React.FC = () => {
  const {
    cart,
    discountPercentage,
    notes,
    updateQuantity,
    removeFromCart,
    clearCart,
    setDiscount,
    setNotes,
  } = usePOSStore();

  const handleQuantityChange = (productId: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId);
      return;
    }
    updateQuantity(productId, newQuantity);
  };

  const handleRemoveItem = (productId: number, productName: string) => {
    removeFromCart(productId);
    toast.success(`${productName} removed from cart`);
  };

  const handleClearCart = () => {
    if (cart.length === 0) return;
    
    clearCart();
    toast.success('Cart cleared');
  };

  const handleDiscountChange = (value: string) => {
    const percentage = parseFloat(value) || 0;
    setDiscount(percentage);
  };

  if (cart.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <ShoppingCart className="h-16 w-16 mb-4" />
        <h3 className="text-lg font-medium mb-2">Cart is empty</h3>
        <p className="text-sm text-center">Add products to start a transaction</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Cart Items */}
      <div className="space-y-3">
        {cart.map((item) => (
          <div key={item.product.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {item.product.name}
              </h4>
              <p className="text-xs text-gray-500">{item.product.sku}</p>
              <p className="text-sm font-medium text-gray-900">
                {formatCurrency(item.unitPrice)} × {item.quantity}
              </p>
            </div>
            
            {/* Quantity Controls */}
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                className="h-8 w-8 p-0"
              >
                <Minus className="h-3 w-3" />
              </Button>
              
              <span className="text-sm font-medium w-8 text-center">
                {item.quantity}
              </span>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                className="h-8 w-8 p-0"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>

            {/* Total Price */}
            <div className="text-right">
              <p className="text-sm font-bold text-gray-900">
                {formatCurrency(item.totalPrice)}
              </p>
            </div>

            {/* Remove Button */}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleRemoveItem(item.product.id, item.product.name)}
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      {/* Discount Input */}
      <div className="pt-4 border-t">
        <Input
          type="number"
          label="Discount (%)"
          placeholder="0"
          value={discountPercentage.toString()}
          onChange={(e) => handleDiscountChange(e.target.value)}
          leftIcon={<Percent className="h-4 w-4" />}
          min="0"
          max="100"
          step="0.1"
        />
      </div>

      {/* Notes Input */}
      <div>
        <Input
          type="text"
          label="Notes (optional)"
          placeholder="Add transaction notes..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>

      {/* Clear Cart Button */}
      <div className="pt-4 border-t">
        <Button
          variant="outline"
          onClick={handleClearCart}
          className="w-full text-red-600 border-red-300 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Cart
        </Button>
      </div>
    </div>
  );
};

export default Cart;

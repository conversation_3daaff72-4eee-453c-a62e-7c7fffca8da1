import { invoke } from '@tauri-apps/api/core';
import { User, ApiResponse } from '@/types';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export class AuthService {
  private static instance: AuthService;
  private token: string | null = null;

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  public setToken(token: string | null): void {
    this.token = token;
  }

  public getToken(): string | null {
    return this.token;
  }

  public async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Call Tauri backend for authentication
      const response = await invoke<ApiResponse<LoginResponse>>('login', {
        credentials,
      });

      if (!response.success || !response.data) {
        throw new Error(response.error || 'Login failed');
      }

      this.setToken(response.data.token);
      return response.data;
    } catch (error) {
      // Fallback to mock data for development
      console.warn('Tauri backend not available, using mock data:', error);
      
      // Mock authentication logic
      if (credentials.username && credentials.password) {
        const mockResponse: LoginResponse = {
          user: {
            id: 1,
            username: credentials.username,
            email: `${credentials.username}@example.com`,
            fullName: 'Admin User',
            role: 'admin',
            isActive: true,
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token',
        };
        
        this.setToken(mockResponse.token);
        return mockResponse;
      } else {
        throw new Error('Invalid credentials');
      }
    }
  }

  public async logout(): Promise<void> {
    try {
      await invoke<ApiResponse<void>>('logout');
    } catch (error) {
      console.warn('Tauri backend not available for logout:', error);
    } finally {
      this.setToken(null);
    }
  }

  public async refreshToken(): Promise<string> {
    try {
      const response = await invoke<ApiResponse<{ token: string }>>('refresh_token');
      
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Token refresh failed');
      }

      this.setToken(response.data.token);
      return response.data.token;
    } catch (error) {
      console.warn('Token refresh failed:', error);
      throw error;
    }
  }

  public async validateToken(): Promise<boolean> {
    if (!this.token) return false;

    try {
      const response = await invoke<ApiResponse<{ valid: boolean }>>('validate_token', {
        token: this.token,
      });

      return response.success && response.data?.valid === true;
    } catch (error) {
      console.warn('Token validation failed:', error);
      return false;
    }
  }

  public async getCurrentUser(): Promise<User | null> {
    if (!this.token) return null;

    try {
      const response = await invoke<ApiResponse<User>>('get_current_user');
      
      if (!response.success || !response.data) {
        return null;
      }

      return response.data;
    } catch (error) {
      console.warn('Failed to get current user:', error);
      return null;
    }
  }
}

export const authService = AuthService.getInstance();

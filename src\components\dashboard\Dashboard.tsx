import React from 'react';
import { Link } from 'react-router-dom';
import { 
  DollarSign, 
  ShoppingCart, 
  Package, 
  Users, 
  TrendingUp, 
  TrendingDown,
  ArrowRight,
  Calendar,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import { formatCurrency, formatNumber, formatDate } from '@/lib/utils';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<{ className?: string }>;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, changeType, icon: Icon }) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            <div className="flex items-center mt-1">
              {changeType === 'increase' ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${
                changeType === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {change}
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
          <div className={`p-3 rounded-full ${
            changeType === 'increase' ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <Icon className={`h-6 w-6 ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface QuickActionProps {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const QuickAction: React.FC<QuickActionProps> = ({ title, description, href, icon: Icon, color }) => {
  return (
    <Link to={href}>
      <Card className="hover:shadow-medium transition-shadow cursor-pointer">
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              <p className="text-sm text-gray-600">{description}</p>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400" />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

const Dashboard: React.FC = () => {
  // Mock data - replace with actual API calls
  const metrics = [
    {
      title: 'Total Revenue',
      value: formatCurrency(15420000),
      change: '+12.5%',
      changeType: 'increase' as const,
      icon: DollarSign,
    },
    {
      title: 'Total Sales',
      value: formatNumber(1234),
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: ShoppingCart,
    },
    {
      title: 'Products Sold',
      value: formatNumber(5678),
      change: '+15.3%',
      changeType: 'increase' as const,
      icon: Package,
    },
    {
      title: 'Active Customers',
      value: formatNumber(892),
      change: '-2.1%',
      changeType: 'decrease' as const,
      icon: Users,
    },
  ];

  const quickActions = [
    {
      title: 'Start New Sale',
      description: 'Process customer transactions',
      href: '/pos',
      icon: ShoppingCart,
      color: 'bg-primary-600',
    },
    {
      title: 'Manage Inventory',
      description: 'Add or update products',
      href: '/inventory',
      icon: Package,
      color: 'bg-green-600',
    },
    {
      title: 'View Reports',
      description: 'Analyze sales performance',
      href: '/reports',
      icon: TrendingUp,
      color: 'bg-purple-600',
    },
    {
      title: 'Customer Management',
      description: 'Manage customer database',
      href: '/customers',
      icon: Users,
      color: 'bg-orange-600',
    },
  ];

  const recentTransactions = [
    { id: 'TRX001', customer: 'John Doe', amount: 125000, time: '10:30 AM' },
    { id: 'TRX002', customer: 'Jane Smith', amount: 89000, time: '10:15 AM' },
    { id: 'TRX003', customer: 'Bob Johnson', amount: 234000, time: '09:45 AM' },
    { id: 'TRX004', customer: 'Alice Brown', amount: 156000, time: '09:30 AM' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your store today.</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          <span>{formatDate(new Date(), 'long')}</span>
        </div>
      </div>

      {/* Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <QuickAction key={index} {...action} />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">{transaction.customer}</p>
                    <p className="text-sm text-gray-500">{transaction.id}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatCurrency(transaction.amount)}</p>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      {transaction.time}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t">
              <Link
                to="/transactions"
                className="text-sm font-medium text-primary-600 hover:text-primary-500 flex items-center"
              >
                View all transactions
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Low Stock Alert */}
        <Card>
          <CardHeader>
            <CardTitle>Low Stock Alert</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Coca Cola 330ml</p>
                  <p className="text-sm text-gray-500">SKU: CC-330</p>
                </div>
                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  5 left
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Indomie Goreng</p>
                  <p className="text-sm text-gray-500">SKU: IM-GOR</p>
                </div>
                <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                  12 left
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Aqua 600ml</p>
                  <p className="text-sm text-gray-500">SKU: AQ-600</p>
                </div>
                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  3 left
                </span>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t">
              <Link
                to="/inventory"
                className="text-sm font-medium text-primary-600 hover:text-primary-500 flex items-center"
              >
                Manage inventory
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
